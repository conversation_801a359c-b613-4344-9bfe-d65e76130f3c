-- Fix RLS recursion issue by using a function-based approach
-- Migration: 20250120000005_fix_rls_recursion.sql

-- First, create a function to check if current user is admin
-- This function will be security definer to avoid recursion
CREATE OR REPLACE FUNCTION public.is_current_user_admin()
RETURNS boolean
LANGUAGE sql
SECURITY DEFINER
STABLE
AS $$
  SELECT COALESCE(
    (SELECT is_admin FROM public.users WHERE id = auth.uid()),
    false
  );
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION public.is_current_user_admin() TO authenticated;

-- Drop all existing policies that might cause recursion
DROP POLICY IF EXISTS "Users can view own data or admins can view all" ON public.users;
DROP POLICY IF EXISTS "Users can update own data or admins can update all" ON public.users;
DROP POLICY IF EXISTS "Users can insert own data" ON public.users;
DROP POLICY IF EXISTS "Service role can do everything with users" ON public.users;
DROP POLICY IF EXISTS "Users can view own data" ON public.users;
DROP POLICY IF EXISTS "Users can update own data" ON public.users;

-- Create simple, non-recursive policies for users table
CREATE POLICY "Users can view own data"
    ON public.users
    FOR SELECT
    TO authenticated
    USING (auth.uid() = id);

CREATE POLICY "Admins can view all users"
    ON public.users
    FOR SELECT
    TO authenticated
    USING (public.is_current_user_admin() = true);

CREATE POLICY "Users can update own data"
    ON public.users
    FOR UPDATE
    TO authenticated
    USING (auth.uid() = id)
    WITH CHECK (auth.uid() = id);

CREATE POLICY "Admins can update all users"
    ON public.users
    FOR UPDATE
    TO authenticated
    USING (public.is_current_user_admin() = true)
    WITH CHECK (public.is_current_user_admin() = true);

CREATE POLICY "Users can insert own data"
    ON public.users
    FOR INSERT
    TO authenticated
    WITH CHECK (auth.uid() = id);

CREATE POLICY "Service role can do everything with users"
    ON public.users
    FOR ALL
    TO service_role
    USING (true);

-- Fix subscriptions policies
DROP POLICY IF EXISTS "Users can view own subscription or admins can view all" ON public.subscriptions;
DROP POLICY IF EXISTS "Users can read own subscription" ON public.subscriptions;
DROP POLICY IF EXISTS "Service role can update subscriptions" ON public.subscriptions;
DROP POLICY IF EXISTS "Service role can do everything with subscriptions" ON public.subscriptions;

CREATE POLICY "Users can view own subscription"
    ON public.subscriptions
    FOR SELECT
    TO authenticated
    USING (auth.uid() = user_id);

CREATE POLICY "Admins can view all subscriptions"
    ON public.subscriptions
    FOR SELECT
    TO authenticated
    USING (public.is_current_user_admin() = true);

CREATE POLICY "Service role can do everything with subscriptions"
    ON public.subscriptions
    FOR ALL
    TO service_role
    USING (true);

-- Fix user_statistics policies
DROP POLICY IF EXISTS "Users can view own stats or admins can view all" ON public.user_statistics;
DROP POLICY IF EXISTS "Users can view own statistics" ON public.user_statistics;
DROP POLICY IF EXISTS "Admins can view all statistics" ON public.user_statistics;

CREATE POLICY "Users can view own statistics"
    ON public.user_statistics
    FOR SELECT
    TO authenticated
    USING (auth.uid() = user_id);

CREATE POLICY "Admins can view all statistics"
    ON public.user_statistics
    FOR SELECT
    TO authenticated
    USING (public.is_current_user_admin() = true);

-- Fix collections policies
DROP POLICY IF EXISTS "Users can view own collections or admins can view all" ON public.collections;
DROP POLICY IF EXISTS "Users can view their own collections" ON public.collections;

CREATE POLICY "Users can view own collections"
    ON public.collections
    FOR SELECT
    TO authenticated
    USING (auth.uid() = user_id);

CREATE POLICY "Admins can view all collections"
    ON public.collections
    FOR SELECT
    TO authenticated
    USING (public.is_current_user_admin() = true);

-- Fix wishlist_cards policies
DROP POLICY IF EXISTS "Users can view own wishlist or admins can view all" ON public.wishlist_cards;
DROP POLICY IF EXISTS "Users can view their own wishlist cards" ON public.wishlist_cards;

CREATE POLICY "Users can view own wishlist"
    ON public.wishlist_cards
    FOR SELECT
    TO authenticated
    USING (auth.uid() = user_id);

CREATE POLICY "Admins can view all wishlist"
    ON public.wishlist_cards
    FOR SELECT
    TO authenticated
    USING (public.is_current_user_admin() = true);

-- Ensure proper permissions
GRANT SELECT, INSERT, UPDATE ON public.users TO authenticated;
GRANT ALL ON public.users TO service_role;
GRANT SELECT ON public.subscriptions TO authenticated;
GRANT ALL ON public.subscriptions TO service_role;
GRANT SELECT ON public.user_statistics TO authenticated;
GRANT ALL ON public.user_statistics TO service_role;

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_users_is_admin ON public.users(is_admin);

-- Refresh the schema cache
NOTIFY pgrst, 'reload schema';

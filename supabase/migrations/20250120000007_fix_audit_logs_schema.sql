-- Fix audit_logs schema and column names
-- Migration: 20250120000007_fix_audit_logs_schema.sql

-- First, let's check the current audit_logs table structure and fix it
-- Add missing columns if they don't exist
DO $$ 
BEGIN
    -- Add action_type column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'audit_logs' AND column_name = 'action_type') THEN
        ALTER TABLE public.audit_logs ADD COLUMN action_type text;
    END IF;
    
    -- Add resource_type column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'audit_logs' AND column_name = 'resource_type') THEN
        ALTER TABLE public.audit_logs ADD COLUMN resource_type text;
    END IF;
    
    -- Add resource_id column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'audit_logs' AND column_name = 'resource_id') THEN
        ALTER TABLE public.audit_logs ADD COLUMN resource_id text;
    END IF;
END $$;

-- If we have old columns with different names, migrate the data
DO $$
BEGIN
    -- Migrate action to action_type if needed
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'audit_logs' AND column_name = 'action') 
       AND EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'audit_logs' AND column_name = 'action_type') THEN
        UPDATE public.audit_logs SET action_type = action WHERE action_type IS NULL;
    END IF;
    
    -- Migrate entity_type to resource_type if needed
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'audit_logs' AND column_name = 'entity_type') 
       AND EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'audit_logs' AND column_name = 'resource_type') THEN
        UPDATE public.audit_logs SET resource_type = entity_type WHERE resource_type IS NULL;
    END IF;
    
    -- Migrate entity_id to resource_id if needed
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'audit_logs' AND column_name = 'entity_id') 
       AND EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'audit_logs' AND column_name = 'resource_id') THEN
        UPDATE public.audit_logs SET resource_id = entity_id WHERE resource_id IS NULL;
    END IF;
END $$;

-- Update the log_admin_action function to match the correct column names
CREATE OR REPLACE FUNCTION public.log_admin_action(
  admin_user_id uuid,
  target_user_id uuid,
  action_type text,
  resource_type text,
  resource_id text,
  old_values jsonb DEFAULT NULL,
  new_values jsonb DEFAULT NULL,
  metadata jsonb DEFAULT NULL
)
RETURNS uuid
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  log_id uuid;
BEGIN
  -- Check if current user is admin
  IF NOT public.is_current_user_admin() AND auth.role() != 'service_role' THEN
    RAISE EXCEPTION 'Unauthorized: Admin access required';
  END IF;

  -- Insert audit log entry with correct column names
  INSERT INTO public.audit_logs (
    admin_user_id,
    target_user_id,
    action_type,
    resource_type,
    resource_id,
    old_values,
    new_values,
    metadata,
    created_at
  ) VALUES (
    admin_user_id,
    target_user_id,
    action_type,
    resource_type,
    resource_id,
    old_values,
    new_values,
    metadata,
    now()
  ) RETURNING id INTO log_id;

  RETURN log_id;
END;
$$;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION public.log_admin_action(uuid, uuid, text, text, text, jsonb, jsonb, jsonb) TO authenticated;
GRANT EXECUTE ON FUNCTION public.log_admin_action(uuid, uuid, text, text, text, jsonb, jsonb, jsonb) TO service_role;

-- Refresh the schema cache
NOTIFY pgrst, 'reload schema';

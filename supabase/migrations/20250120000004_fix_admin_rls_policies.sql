-- Fix RLS policies to allow admin users to access all user data
-- Migration: 20250120000004_fix_admin_rls_policies.sql

-- Drop existing policies for users table
DROP POLICY IF EXISTS "Users can view own data" ON public.users;
DROP POLICY IF EXISTS "Users can update own data" ON public.users;
DROP POLICY IF EXISTS "Users can insert own data" ON public.users;
DROP POLICY IF EXISTS "Service role can do everything with users" ON public.users;
DROP POLICY IF EXISTS "Users can view own profile" ON public.users;
DROP POLICY IF EXISTS "Users can update own profile" ON public.users;
DROP POLICY IF EXISTS "Enable insert for registration" ON public.users;

-- Create new policies that allow admin access
-- Policy for SELECT: Users can view own data OR admins can view all data
CREATE POLICY "Users can view own data or admins can view all"
    ON public.users
    FOR SELECT
    TO authenticated
    USING (
        auth.uid() = id 
        OR EXISTS (
            SELECT 1 FROM public.users 
            WHERE id = auth.uid() AND is_admin = true
        )
        OR auth.role() = 'service_role'
    );

-- Policy for UPDATE: Users can update own data OR admins can update any data
CREATE POLICY "Users can update own data or admins can update all"
    ON public.users
    FOR UPDATE
    TO authenticated
    USING (
        auth.uid() = id 
        OR EXISTS (
            SELECT 1 FROM public.users 
            WHERE id = auth.uid() AND is_admin = true
        )
    )
    WITH CHECK (
        auth.uid() = id 
        OR EXISTS (
            SELECT 1 FROM public.users 
            WHERE id = auth.uid() AND is_admin = true
        )
    );

-- Policy for INSERT: Users can insert own data during registration
CREATE POLICY "Users can insert own data"
    ON public.users
    FOR INSERT
    TO authenticated
    WITH CHECK (auth.uid() = id);

-- Service role policy (for system operations)
CREATE POLICY "Service role can do everything with users"
    ON public.users
    FOR ALL
    TO service_role
    USING (true);

-- Fix subscriptions policies to allow admin access
DROP POLICY IF EXISTS "Users can read own subscription" ON public.subscriptions;
DROP POLICY IF EXISTS "Service role can update subscriptions" ON public.subscriptions;

-- Create new subscription policies
CREATE POLICY "Users can view own subscription or admins can view all"
    ON public.subscriptions
    FOR SELECT
    TO authenticated
    USING (
        auth.uid() = user_id 
        OR EXISTS (
            SELECT 1 FROM public.users 
            WHERE id = auth.uid() AND is_admin = true
        )
        OR auth.role() = 'service_role'
    );

-- Service role policy for subscriptions
CREATE POLICY "Service role can do everything with subscriptions"
    ON public.subscriptions
    FOR ALL
    TO service_role
    USING (true);

-- Fix user_statistics policies to allow admin access
DROP POLICY IF EXISTS "Users can view own statistics" ON public.user_statistics;
DROP POLICY IF EXISTS "Admins can view all statistics" ON public.user_statistics;

-- Create new user_statistics policy
CREATE POLICY "Users can view own stats or admins can view all"
    ON public.user_statistics
    FOR SELECT
    TO authenticated
    USING (
        auth.uid() = user_id 
        OR EXISTS (
            SELECT 1 FROM public.users 
            WHERE id = auth.uid() AND is_admin = true
        )
        OR auth.role() = 'service_role'
    );

-- Fix collections policies to allow admin access
DROP POLICY IF EXISTS "Users can view their own collections" ON public.collections;

CREATE POLICY "Users can view own collections or admins can view all"
    ON public.collections
    FOR SELECT
    TO authenticated
    USING (
        auth.uid() = user_id 
        OR EXISTS (
            SELECT 1 FROM public.users 
            WHERE id = auth.uid() AND is_admin = true
        )
        OR auth.role() = 'service_role'
    );

-- Fix wishlist_cards policies to allow admin access
DROP POLICY IF EXISTS "Users can view their own wishlist cards" ON public.wishlist_cards;

CREATE POLICY "Users can view own wishlist or admins can view all"
    ON public.wishlist_cards
    FOR SELECT
    TO authenticated
    USING (
        auth.uid() = user_id 
        OR EXISTS (
            SELECT 1 FROM public.users 
            WHERE id = auth.uid() AND is_admin = true
        )
        OR auth.role() = 'service_role'
    );

-- Ensure proper permissions
GRANT SELECT, INSERT, UPDATE ON public.users TO authenticated;
GRANT ALL ON public.users TO service_role;
GRANT SELECT ON public.subscriptions TO authenticated;
GRANT ALL ON public.subscriptions TO service_role;
GRANT SELECT ON public.user_statistics TO authenticated;
GRANT ALL ON public.user_statistics TO service_role;

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_users_is_admin ON public.users(is_admin);
CREATE INDEX IF NOT EXISTS idx_users_auth_lookup ON public.users(id) WHERE is_admin = true;

-- Refresh the schema cache
NOTIFY pgrst, 'reload schema';

-- Fix foreign key relationships for admin queries
-- Migration: 20250120000003_fix_foreign_key_relationships.sql

-- The issue is that both users and subscriptions reference auth.users(id)
-- but there's no direct relationship between public.users and public.subscriptions
-- We need to ensure Supabase can understand the relationship through the shared auth.users reference

-- First, let's check if we need to add any missing foreign key constraints
-- The subscriptions table should reference public.users, not auth.users directly

-- Drop the existing foreign key constraint on subscriptions.user_id if it exists
ALTER TABLE public.subscriptions 
DROP CONSTRAINT IF EXISTS subscriptions_user_id_fkey;

-- Add a proper foreign key constraint that references public.users
-- This will allow <PERSON><PERSON><PERSON> to understand the relationship for joins
ALTER TABLE public.subscriptions 
ADD CONSTRAINT subscriptions_user_id_fkey 
FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;

-- Do the same for user_statistics
ALTER TABLE public.user_statistics 
DROP CONSTRAINT IF EXISTS user_statistics_user_id_fkey;

ALTER TABLE public.user_statistics 
ADD CONSTRAINT user_statistics_user_id_fkey 
FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;

-- Do the same for subscription_overrides
ALTER TABLE public.subscription_overrides 
DROP CONSTRAINT IF EXISTS subscription_overrides_user_id_fkey;

ALTER TABLE public.subscription_overrides 
ADD CONSTRAINT subscription_overrides_user_id_fkey 
FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;

-- Update the admin_user_id foreign key in subscription_overrides to reference public.users
ALTER TABLE public.subscription_overrides 
DROP CONSTRAINT IF EXISTS subscription_overrides_admin_user_id_fkey;

ALTER TABLE public.subscription_overrides 
ADD CONSTRAINT subscription_overrides_admin_user_id_fkey 
FOREIGN KEY (admin_user_id) REFERENCES public.users(id) ON DELETE SET NULL;

-- Update audit_logs foreign keys to reference public.users
ALTER TABLE public.audit_logs 
DROP CONSTRAINT IF EXISTS audit_logs_admin_user_id_fkey;

ALTER TABLE public.audit_logs 
ADD CONSTRAINT audit_logs_admin_user_id_fkey 
FOREIGN KEY (admin_user_id) REFERENCES public.users(id) ON DELETE SET NULL;

ALTER TABLE public.audit_logs 
DROP CONSTRAINT IF EXISTS audit_logs_target_user_id_fkey;

ALTER TABLE public.audit_logs 
ADD CONSTRAINT audit_logs_target_user_id_fkey 
FOREIGN KEY (target_user_id) REFERENCES public.users(id) ON DELETE SET NULL;

-- Update collections to reference public.users instead of auth.users
ALTER TABLE public.collections 
DROP CONSTRAINT IF EXISTS collections_user_id_fkey;

ALTER TABLE public.collections 
ADD CONSTRAINT collections_user_id_fkey 
FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;

-- Update wishlist_cards to reference public.users instead of auth.users
ALTER TABLE public.wishlist_cards 
DROP CONSTRAINT IF EXISTS wishlist_cards_user_id_fkey;

ALTER TABLE public.wishlist_cards 
ADD CONSTRAINT wishlist_cards_user_id_fkey 
FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;

-- Update subscription_changes to reference public.users instead of auth.users
ALTER TABLE public.subscription_changes 
DROP CONSTRAINT IF EXISTS subscription_changes_user_id_fkey;

ALTER TABLE public.subscription_changes 
ADD CONSTRAINT subscription_changes_user_id_fkey 
FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;

-- Add indexes to improve performance for the new foreign key relationships
CREATE INDEX IF NOT EXISTS idx_subscriptions_user_id_fk ON public.subscriptions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_statistics_user_id_fk ON public.user_statistics(user_id);
CREATE INDEX IF NOT EXISTS idx_subscription_overrides_user_id_fk ON public.subscription_overrides(user_id);
CREATE INDEX IF NOT EXISTS idx_subscription_overrides_admin_user_id_fk ON public.subscription_overrides(admin_user_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_admin_user_id_fk ON public.audit_logs(admin_user_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_target_user_id_fk ON public.audit_logs(target_user_id);
CREATE INDEX IF NOT EXISTS idx_collections_user_id_fk ON public.collections(user_id);
CREATE INDEX IF NOT EXISTS idx_wishlist_cards_user_id_fk ON public.wishlist_cards(user_id);
CREATE INDEX IF NOT EXISTS idx_subscription_changes_user_id_fk ON public.subscription_changes(user_id);

-- Add comments for documentation
COMMENT ON CONSTRAINT subscriptions_user_id_fkey ON public.subscriptions IS 'Foreign key to public.users for admin queries';
COMMENT ON CONSTRAINT user_statistics_user_id_fkey ON public.user_statistics IS 'Foreign key to public.users for admin queries';
COMMENT ON CONSTRAINT subscription_overrides_user_id_fkey ON public.subscription_overrides IS 'Foreign key to public.users for admin queries';

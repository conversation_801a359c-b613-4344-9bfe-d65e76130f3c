-- Fix all remaining RLS policies for complete admin access
-- Migration: 20250120000006_fix_all_admin_rls_policies.sql

-- First, ensure the admin check function is working correctly
CREATE OR REPLACE FUNCTION public.is_current_user_admin()
RETURNS boolean
LANGUAGE sql
SECURITY DEFINER
STABLE
AS $$
  SELECT COALESCE(
    (SELECT is_admin FROM public.users WHERE id = auth.uid()),
    false
  );
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION public.is_current_user_admin() TO authenticated;
GRANT EXECUTE ON FUNCTION public.is_current_user_admin() TO service_role;

-- Create a function to check if user is admin or service role
CREATE OR REPLACE FUNCTION public.is_admin_or_service_role()
RETURNS boolean
LANGUAGE sql
SECURITY DEFINER
STABLE
AS $$
  SELECT 
    auth.role() = 'service_role' OR 
    COALESCE((SELECT is_admin FROM public.users WHERE id = auth.uid()), false);
$$;

GRANT EXECUTE ON FUNCTION public.is_admin_or_service_role() TO authenticated;
GRANT EXECUTE ON FUNCTION public.is_admin_or_service_role() TO service_role;

-- Fix audit_logs policies (this is causing the 403 error)
DROP POLICY IF EXISTS "Admins can view all audit logs" ON public.audit_logs;
DROP POLICY IF EXISTS "Admins can insert audit logs" ON public.audit_logs;
DROP POLICY IF EXISTS "Service role can do everything with audit logs" ON public.audit_logs;

-- Create comprehensive audit_logs policies
CREATE POLICY "Admins can view all audit logs"
    ON public.audit_logs
    FOR SELECT
    TO authenticated
    USING (public.is_current_user_admin() = true);

CREATE POLICY "Admins can insert audit logs"
    ON public.audit_logs
    FOR INSERT
    TO authenticated
    WITH CHECK (public.is_current_user_admin() = true);

CREATE POLICY "Service role can do everything with audit logs"
    ON public.audit_logs
    FOR ALL
    TO service_role
    USING (true);

-- Fix user_statistics policies for admin access
DROP POLICY IF EXISTS "Users can view own stats or admins can view all" ON public.user_statistics;
DROP POLICY IF EXISTS "Admins can view all statistics" ON public.user_statistics;

CREATE POLICY "Users can view own statistics"
    ON public.user_statistics
    FOR SELECT
    TO authenticated
    USING (auth.uid() = user_id);

CREATE POLICY "Admins can view all statistics"
    ON public.user_statistics
    FOR SELECT
    TO authenticated
    USING (public.is_current_user_admin() = true);

CREATE POLICY "Service role can do everything with user statistics"
    ON public.user_statistics
    FOR ALL
    TO service_role
    USING (true);

-- Fix subscription_overrides policies
DROP POLICY IF EXISTS "Admins can view all subscription overrides" ON public.subscription_overrides;
DROP POLICY IF EXISTS "Admins can manage subscription overrides" ON public.subscription_overrides;

CREATE POLICY "Admins can view all subscription overrides"
    ON public.subscription_overrides
    FOR SELECT
    TO authenticated
    USING (public.is_current_user_admin() = true);

CREATE POLICY "Admins can manage subscription overrides"
    ON public.subscription_overrides
    FOR ALL
    TO authenticated
    USING (public.is_current_user_admin() = true);

CREATE POLICY "Service role can do everything with subscription overrides"
    ON public.subscription_overrides
    FOR ALL
    TO service_role
    USING (true);

-- Ensure collections policies allow admin access
DROP POLICY IF EXISTS "Admins can view all collections" ON public.collections;

CREATE POLICY "Admins can view all collections"
    ON public.collections
    FOR SELECT
    TO authenticated
    USING (public.is_current_user_admin() = true);

-- Ensure collection_cards policies allow admin access
DROP POLICY IF EXISTS "Admins can view all collection cards" ON public.collection_cards;

CREATE POLICY "Admins can view all collection cards"
    ON public.collection_cards
    FOR SELECT
    TO authenticated
    USING (public.is_current_user_admin() = true);

-- Ensure wishlist_cards policies allow admin access (already created but let's make sure)
DROP POLICY IF EXISTS "Admins can view all wishlist" ON public.wishlist_cards;

CREATE POLICY "Admins can view all wishlist"
    ON public.wishlist_cards
    FOR SELECT
    TO authenticated
    USING (public.is_current_user_admin() = true);

-- Create RPC function for admin operations that bypasses RLS
CREATE OR REPLACE FUNCTION public.admin_get_all_users(
  page_num integer DEFAULT 1,
  page_size integer DEFAULT 20,
  search_email text DEFAULT ''
)
RETURNS TABLE(
  id uuid,
  email text,
  full_name text,
  is_admin boolean,
  is_active boolean,
  created_at timestamptz,
  updated_at timestamptz,
  last_login_at timestamptz,
  login_count integer,
  total_count bigint
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  offset_val integer;
  total_users bigint;
BEGIN
  -- Check if current user is admin
  IF NOT public.is_current_user_admin() AND auth.role() != 'service_role' THEN
    RAISE EXCEPTION 'Unauthorized: Admin access required';
  END IF;

  offset_val := (page_num - 1) * page_size;
  
  -- Get total count
  SELECT COUNT(*) INTO total_users
  FROM public.users u
  WHERE (search_email = '' OR u.email ILIKE '%' || search_email || '%');

  -- Return paginated results with total count
  RETURN QUERY
  SELECT 
    u.id,
    u.email,
    u.full_name,
    u.is_admin,
    u.is_active,
    u.created_at,
    u.updated_at,
    u.last_login_at,
    u.login_count,
    total_users as total_count
  FROM public.users u
  WHERE (search_email = '' OR u.email ILIKE '%' || search_email || '%')
  ORDER BY u.created_at DESC
  LIMIT page_size
  OFFSET offset_val;
END;
$$;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION public.admin_get_all_users(integer, integer, text) TO authenticated;
GRANT EXECUTE ON FUNCTION public.admin_get_all_users(integer, integer, text) TO service_role;

-- Create function for logging admin actions that bypasses RLS
CREATE OR REPLACE FUNCTION public.log_admin_action(
  admin_user_id uuid,
  target_user_id uuid,
  action_type text,
  resource_type text,
  resource_id text,
  old_values jsonb DEFAULT NULL,
  new_values jsonb DEFAULT NULL,
  metadata jsonb DEFAULT NULL
)
RETURNS uuid
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  log_id uuid;
BEGIN
  -- Check if current user is admin
  IF NOT public.is_current_user_admin() AND auth.role() != 'service_role' THEN
    RAISE EXCEPTION 'Unauthorized: Admin access required';
  END IF;

  -- Insert audit log entry
  INSERT INTO public.audit_logs (
    admin_user_id,
    target_user_id,
    action_type,
    resource_type,
    resource_id,
    old_values,
    new_values,
    metadata,
    created_at
  ) VALUES (
    admin_user_id,
    target_user_id,
    action_type,
    resource_type,
    resource_id,
    old_values,
    new_values,
    metadata,
    now()
  ) RETURNING id INTO log_id;

  RETURN log_id;
END;
$$;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION public.log_admin_action(uuid, uuid, text, text, text, jsonb, jsonb, jsonb) TO authenticated;
GRANT EXECUTE ON FUNCTION public.log_admin_action(uuid, uuid, text, text, text, jsonb, jsonb, jsonb) TO service_role;

-- Ensure proper permissions on all tables
GRANT SELECT ON public.users TO authenticated;
GRANT SELECT ON public.subscriptions TO authenticated;
GRANT SELECT ON public.user_statistics TO authenticated;
GRANT SELECT ON public.audit_logs TO authenticated;
GRANT SELECT ON public.subscription_overrides TO authenticated;
GRANT SELECT ON public.collections TO authenticated;
GRANT SELECT ON public.collection_cards TO authenticated;
GRANT SELECT ON public.wishlist_cards TO authenticated;

-- Grant full access to service role
GRANT ALL ON ALL TABLES IN SCHEMA public TO service_role;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO service_role;
GRANT ALL ON ALL FUNCTIONS IN SCHEMA public TO service_role;

-- Refresh the schema cache
NOTIFY pgrst, 'reload schema';

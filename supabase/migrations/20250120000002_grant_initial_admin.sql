-- Grant initial admin privileges
-- Migration: 20250120000002_grant_initial_admin.sql
-- This migration creates a temporary function to grant admin privileges

-- Create a function to grant admin privileges to the first user (for initial setup)
CREATE OR REPLACE FUNCTION grant_initial_admin()
<PERSON><PERSON><PERSON><PERSON> void AS $$
DECLARE
    first_user_id UUID;
BEGIN
    -- Get the first user (usually the developer/owner)
    SELECT id INTO first_user_id 
    FROM public.users 
    ORDER BY created_at ASC 
    LIMIT 1;
    
    -- Grant admin privileges if user exists
    IF first_user_id IS NOT NULL THEN
        UPDATE public.users 
        SET is_admin = true 
        WHERE id = first_user_id;
        
        RAISE NOTICE 'Admin privileges granted to user: %', first_user_id;
    ELSE
        RAISE NOTICE 'No users found to grant admin privileges';
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION grant_initial_admin() TO authenticated;
GRANT EXECUTE ON FUNCTION grant_initial_admin() TO service_role;

-- You can run this function manually in the SQL editor:
-- SELECT grant_initial_admin();

-- Or grant admin to a specific user by email:
-- UPDATE public.users SET is_admin = true WHERE email = '<EMAIL>';
